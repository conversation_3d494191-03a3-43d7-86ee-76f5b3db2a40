<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>The MusicArt Club</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/heroicons/2.1.3/24/outline/styles.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: { // Orange
              light: '#fbbf24', // orange-400
              DEFAULT: '#f97316', // orange-500
              dark: '#ea580c', // orange-600
            },
            secondary: { // Deep Red/Maroon
              light: '#dc2626', // red-600
              DEFAULT: '#b91c1c', // red-700
              dark: '#991b1b', // red-800
            },
            accent: { // Yellow
              light: '#fde047', // yellow-300
              DEFAULT: '#facc15', // yellow-400
              dark: '#eab308', // yellow-500
            },
            neutral: {
              lightest: '#f8fafc', // slate-50
              light: '#f1f5f9',    // slate-100
              DEFAULT: '#64748b', // slate-500
              dark: '#334155',    // slate-700
              darkest: '#0f172a'  // slate-900
            }
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
        }
      }
    }
  </script>
  <style>
    /* For Heroicons compatibility if needed, or simple icon styling */
    .icon {
      width: 1.5rem;
      height: 1.5rem;
      display: inline-block;
      vertical-align: middle;
    }
    body {
      background-color: tailwind.theme.colors.neutral.lightest; /* Ensure body bg uses theme */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0"
  }
}
</script>
</head>
<body class="bg-neutral-lightest text-neutral-darkest font-sans antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
