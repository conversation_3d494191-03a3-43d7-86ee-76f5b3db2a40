import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button, Input, Select } from '../components';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { UserRole } from '../types';

const AuthPage: React.FC = () => {
  const { login, signup, currentUser } = useApp();
  const navigate = useNavigate();
  const location = useLocation();
  
  const queryParams = new URLSearchParams(location.search);
  const initialAction = queryParams.get('action') === 'signup' ? 'signup' : 'login';
  const initialRole = queryParams.get('role') as UserRole || UserRole.FAN;

  const [isLoginView, setIsLoginView] = useState(initialAction === 'login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [role, setRole] = useState<UserRole>(initialRole);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentUser) {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);
  
  useEffect(() => {
    setIsLoginView(initialAction === 'login');
    setRole(initialRole);
  }, [location.search, initialAction, initialRole]);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    try {
      if (isLoginView) {
        const user = await login(email, password);
        if (user) navigate('/dashboard');
        else setError('Login failed. Please check your credentials.');
      } else {
        if (!displayName) {
          setError('Display name is required for signup.');
          setIsLoading(false);
          return;
        }
        const user = await signup({ email, password, displayName, role });
        if (user) navigate('/dashboard');
        else setError('Signup failed. User might already exist or invalid data.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageContainer title={isLoginView ? 'Login to Your Account' : 'Create an Account'}>
      <div className="max-w-md mx-auto mt-8 bg-white p-8 shadow-xl rounded-lg">
        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            label="Email Address"
            type="email"
            name="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            placeholder="<EMAIL>"
          />
          <Input
            label="Password"
            type="password"
            name="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            placeholder="••••••••"
          />
          {!isLoginView && (
            <>
              <Input
                label="Display Name"
                type="text"
                name="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                required
                placeholder="Your Name or Stage Name"
              />
              <Select
                label="I am a..."
                name="role"
                value={role}
                onChange={(e) => setRole(e.target.value as UserRole)}
                options={Object.values(UserRole).map(r => ({ value: r, label: r }))}
                required
              />
            </>
          )}
          {error && <p className="text-red-500 text-sm">{error}</p>}
          <Button type="submit" variant="primary" className="w-full" isLoading={isLoading}>
            {isLoginView ? 'Login' : 'Sign Up'}
          </Button>
        </form>
        <p className="mt-6 text-center text-sm text-neutral-dark">
          {isLoginView ? "Don't have an account? " : 'Already have an account? '}
          <button
            onClick={() => { setIsLoginView(!isLoginView); setError(''); }}
            className="font-medium text-primary hover:text-primary-dark transition-colors"
          >
            {isLoginView ? 'Sign Up' : 'Login'}
          </button>
        </p>
      </div>
    </PageContainer>
  );
};

export default AuthPage;