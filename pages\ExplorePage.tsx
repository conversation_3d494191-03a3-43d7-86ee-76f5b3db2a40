
import React, { useState, useMemo } from 'react';
import { PageContainer } from '../components/Layout';
import { FeedItem, EventItem, Button, Input, Select, Icon } from '../components';
import { useApp } from '../AppContext';
import { Post, Event, PostCategory, User } from '../types';

interface ExplorePageProps {
  tab?: 'feed' | 'events';
}

const ExplorePage: React.FC<ExplorePageProps> = ({ tab = 'feed' }) => {
  const { getPosts, getEvents, getUserById, users } = useApp();
  const [activeTab, setActiveTab] = useState<'feed' | 'events'>(tab);

  // Filters for Feed
  const [searchTermFeed, setSearchTermFeed] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<PostCategory | ''>('');
  const [artistFilterFeed, setArtistFilterFeed] = useState<string>(''); // User ID

  // Filters for Events
  const [searchTermEvents, setSearchTermEvents] = useState('');
  const [educatorFilterEvents, setEducatorFilterEvents] = useState<string>(''); // User ID
  const [eventFormatFilter, setEventFormatFilter] = useState<'virtual' | 'in-person' | ''>('');


  const artists = useMemo(() => users.filter(u => u.role === 'Artist'), [users]);
  const educators = useMemo(() => users.filter(u => u.role === 'Educator'), [users]);

  const filteredPosts = useMemo(() => {
    return getPosts()
      .filter(post => 
        (post.title.toLowerCase().includes(searchTermFeed.toLowerCase()) || 
         post.description.toLowerCase().includes(searchTermFeed.toLowerCase()) ||
         post.tags.some(tag => tag.toLowerCase().includes(searchTermFeed.toLowerCase()))) &&
        (categoryFilter ? post.category === categoryFilter : true) &&
        (artistFilterFeed ? post.userId === artistFilterFeed : true)
      );
  }, [getPosts, searchTermFeed, categoryFilter, artistFilterFeed]);

  const filteredEvents = useMemo(() => {
    return getEvents()
      .filter(event => 
        (event.title.toLowerCase().includes(searchTermEvents.toLowerCase()) || 
         event.description.toLowerCase().includes(searchTermEvents.toLowerCase())) &&
        (educatorFilterEvents ? event.educatorId === educatorFilterEvents : true) &&
        (eventFormatFilter ? event.format === eventFormatFilter : true)
      );
  }, [getEvents, searchTermEvents, educatorFilterEvents, eventFormatFilter]);

  const TabButton: React.FC<{label: string, isActive: boolean, onClick: () => void}> = ({label, isActive, onClick}) => (
     <Button
        variant={isActive ? 'primary' : 'ghost'}
        onClick={onClick}
        className={`mr-2 mb-2 ${isActive ? '' : 'text-neutral-dark'}`}
      >
        {label}
      </Button>
  );

  return (
    <PageContainer title="Explore">
      <div className="mb-6 flex flex-wrap border-b border-neutral-light pb-2">
        <TabButton label="Creative Feed" isActive={activeTab === 'feed'} onClick={() => setActiveTab('feed')} />
        <TabButton label="Events & Workshops" isActive={activeTab === 'events'} onClick={() => setActiveTab('events')} />
      </div>

      {activeTab === 'feed' && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Discover Art & Music</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 p-4 bg-neutral-lightest rounded-lg shadow">
            <Input 
              placeholder="Search posts..." 
              value={searchTermFeed} 
              onChange={e => setSearchTermFeed(e.target.value)}
              icon={<Icon name="magnifyingGlass" className="w-5 h-5 text-neutral-dark" />}
            />
            <Select 
              value={categoryFilter} 
              onChange={e => setCategoryFilter(e.target.value as PostCategory | '')}
              options={[{value: '', label: 'All Categories'}, ...Object.values(PostCategory).map(c => ({value: c, label: c}))]}
            />
            <Select 
              value={artistFilterFeed} 
              onChange={e => setArtistFilterFeed(e.target.value)}
              options={[{value: '', label: 'All Artists'}, ...artists.map(a => ({value: a.id, label: a.displayName}))]}
            />
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.length > 0 ? filteredPosts.map(post => (
              <FeedItem key={post.id} post={post} author={getUserById(post.userId) || undefined} />
            )) : <p className="text-neutral-dark col-span-full text-center py-8">No posts match your criteria. Try broadening your search!</p>}
          </div>
        </div>
      )}

      {activeTab === 'events' && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Find Events & Workshops</h2>
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 p-4 bg-neutral-lightest rounded-lg shadow">
            <Input 
              placeholder="Search events..." 
              value={searchTermEvents} 
              onChange={e => setSearchTermEvents(e.target.value)}
              icon={<Icon name="magnifyingGlass" className="w-5 h-5 text-neutral-dark" />}
            />
             <Select 
              value={educatorFilterEvents} 
              onChange={e => setEducatorFilterEvents(e.target.value)}
              options={[{value: '', label: 'All Educators'}, ...educators.map(e => ({value: e.id, label: e.displayName}))]}
            />
            <Select 
              value={eventFormatFilter} 
              onChange={e => setEventFormatFilter(e.target.value as 'virtual' | 'in-person' | '')}
              options={[
                {value: '', label: 'All Formats'},
                {value: 'virtual', label: 'Virtual'},
                {value: 'in-person', label: 'In-Person'}
              ]}
            />
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            {filteredEvents.length > 0 ? filteredEvents.map(event => (
              <EventItem key={event.id} event={event} educator={getUserById(event.educatorId) || undefined} />
            )) : <p className="text-neutral-dark col-span-full text-center py-8">No events match your criteria. Check back soon!</p>}
          </div>
        </div>
      )}
    </PageContainer>
  );
};

export default ExplorePage;
