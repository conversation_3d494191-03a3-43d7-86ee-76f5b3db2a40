
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { User, Post, Event, UserRole, AppContextType, PostCategory, SocialLinks } from './types';

// Fix: Export AppContext so it can be imported by other modules.
export const AppContext = createContext<AppContextType | undefined>(undefined);

const initialUsers: User[] = [
  { id: 'user1', email: '<EMAIL>', password: 'password', role: UserRole.ARTIST, displayName: 'Artistic Annie', profilePhotoUrl: 'https://picsum.photos/seed/annie/200/200', bannerImageUrl: 'https://picsum.photos/seed/annie-banner/1200/400', bio: 'Passionate painter and sculptor.', tags: ['Oil Painter', 'Abstract', 'Modern Art'], socialLinks: { instagram: 'https://instagram.com/annie'}, artistStatement: 'My art explores the intersection of nature and urban life.', genre: ['Abstract', 'Impressionism'] },
  { id: 'user2', email: '<EMAIL>', password: 'password', role: UserRole.EDUCATOR, displayName: 'Musical Mark', profilePhotoUrl: 'https://picsum.photos/seed/mark/200/200', bannerImageUrl: 'https://picsum.photos/seed/mark-banner/1200/400', bio: 'Experienced guitar teacher for all levels.', tags: ['Guitar Coach', 'Music Theory', 'Rock'], socialLinks: { youtube: 'https://youtube.com/mark'}, lessonsOffered: [{id: 'lesson1', title: 'Beginner Guitar', description: 'Learn the basics of guitar playing.'}]},
  { id: 'user3', email: '<EMAIL>', password: 'password', role: UserRole.FAN, displayName: 'Creative Chris', profilePhotoUrl: 'https://picsum.photos/seed/chris/200/200', bannerImageUrl: 'https://picsum.photos/seed/chris-banner/1200/400', bio: 'Loves discovering new artists and music.', tags: ['Music Lover', 'Art Enthusiast'], socialLinks: {}, followedArtistIds: ['user1'], savedPostIds: ['post1'], rsvpedEventIds: ['event1'] },
];

const initialPosts: Post[] = [
  { id: 'post1', userId: 'user1', title: 'Sunset Overdrive', description: 'My latest abstract piece, inspired by city sunsets.', mediaUrl: 'https://picsum.photos/seed/sunsetart/600/400', mediaType: 'image', category: PostCategory.VISUAL_ART, tags: ['Abstract', 'Painting', 'Cityscape'], likes: 15, createdAt: new Date(Date.now() - 86400000 * 2).toISOString() },
  { id: 'post2', userId: 'user1', title: 'Melody in Motion', description: 'A short original composition.', mediaUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', mediaType: 'soundcloud', category: PostCategory.MUSIC, tags: ['Original', 'Piano', 'Instrumental'], likes: 22, createdAt: new Date(Date.now() - 86400000 * 1).toISOString() },
];

const initialEvents: Event[] = [
  { id: 'event1', educatorId: 'user2', title: 'Guitar Workshop: Blues Riffs', description: 'Learn classic blues guitar riffs and techniques. All levels welcome!', dateTime: new Date(Date.now() + 86400000 * 7).toISOString(), format: 'virtual', locationOrLink: 'Zoom Link Provided on RSVP', price: '$20', rsvps: [], createdAt: new Date().toISOString() },
];

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [posts, setPosts] = useState<Post[]>(initialPosts);
  const [events, setEvents] = useState<Event[]>(initialEvents);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const storedUser = localStorage.getItem('currentUser');
    const storedUsers = localStorage.getItem('mac_users');
    const storedPosts = localStorage.getItem('mac_posts');
    const storedEvents = localStorage.getItem('mac_events');

    if (storedUser) setCurrentUser(JSON.parse(storedUser));
    if (storedUsers) setUsers(JSON.parse(storedUsers)); else setUsers(initialUsers);
    if (storedPosts) setPosts(JSON.parse(storedPosts)); else setPosts(initialPosts);
    if (storedEvents) setEvents(JSON.parse(storedEvents)); else setEvents(initialEvents);
    
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
  }, [currentUser, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_users', JSON.stringify(users));
  }, [users, isInitialized]);

  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_posts', JSON.stringify(posts));
  }, [posts, isInitialized]);
  
  useEffect(() => {
    if (!isInitialized) return;
    localStorage.setItem('mac_events', JSON.stringify(events));
  }, [events, isInitialized]);


  const login = useCallback(async (email: string, password_param: string): Promise<User | null> => {
    const user = users.find(u => u.email === email && u.password === password_param);
    if (user) {
      setCurrentUser(user);
      return user;
    }
    alert('Invalid credentials');
    return null;
  }, [users]);

  const signup = useCallback(async (userData: Omit<User, 'id' | 'profilePhotoUrl' | 'bannerImageUrl' | 'tags' | 'socialLinks'> & Partial<Pick<User, 'tags' | 'socialLinks'>>): Promise<User | null> => {
    if (users.find(u => u.email === userData.email)) {
      alert('User already exists with this email.');
      return null;
    }
    const newUser: User = {
      id: `user${Date.now()}`,
      ...userData,
      profilePhotoUrl: `https://picsum.photos/seed/${userData.displayName.split(' ')[0]}/200/200`,
      bannerImageUrl: `https://picsum.photos/seed/${userData.displayName.split(' ')[0]}-banner/1200/400`,
      tags: userData.tags || [],
      socialLinks: userData.socialLinks || {},
      lessonsOffered: userData.role === UserRole.EDUCATOR ? [] : undefined,
      followedArtistIds: userData.role === UserRole.FAN ? [] : undefined,
      savedPostIds: userData.role === UserRole.FAN ? [] : undefined,
      rsvpedEventIds: userData.role === UserRole.FAN ? [] : undefined,
    };
    setUsers(prev => [...prev, newUser]);
    setCurrentUser(newUser);
    return newUser;
  }, [users]);

  const logout = useCallback(() => {
    setCurrentUser(null);
  }, []);

  const updateUserProfile = useCallback(async (userId: string, data: Partial<User>): Promise<User | null> => {
    let updatedUser: User | null = null;
    setUsers(prevUsers => prevUsers.map(u => {
      if (u.id === userId) {
        updatedUser = { ...u, ...data };
        return updatedUser;
      }
      return u;
    }));
    if (updatedUser && currentUser?.id === userId) {
      setCurrentUser(updatedUser);
    }
    return updatedUser;
  }, [currentUser]);

  const getUserById = useCallback((userId: string): User | null => {
    return users.find(u => u.id === userId) || null;
  }, [users]);

  const createPost = useCallback(async (postData: Omit<Post, 'id' | 'likes' | 'createdAt'>): Promise<Post | null> => {
    if (!currentUser || currentUser.role !== UserRole.ARTIST) {
        alert("Only artists can create posts.");
        return null;
    }
    const newPost: Post = {
      ...postData,
      id: `post${Date.now()}`,
      likes: 0,
      createdAt: new Date().toISOString(),
    };
    setPosts(prev => [newPost, ...prev]);
    return newPost;
  }, [currentUser]);

  const getPosts = useCallback((filters?: { category?: PostCategory; userId?: string }): Post[] => {
    return posts
      .filter(p => (!filters?.category || p.category === filters.category))
      .filter(p => (!filters?.userId || p.userId === filters.userId))
      .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [posts]);

  const createEvent = useCallback(async (eventData: Omit<Event, 'id' | 'rsvps' | 'createdAt'>): Promise<Event | null> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
        alert("Only educators can create events.");
        return null;
    }
    const newEvent: Event = {
      ...eventData,
      id: `event${Date.now()}`,
      rsvps: [],
      createdAt: new Date().toISOString(),
    };
    setEvents(prev => [newEvent, ...prev]);
    return newEvent;
  }, [currentUser]);

  const getEvents = useCallback((filters?: { educatorId?: string }): Event[] => {
    return events
      .filter(e => (!filters?.educatorId || e.educatorId === filters.educatorId))
      .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [events]);

  const rsvpToEvent = useCallback(async (eventId: string, userId: string): Promise<boolean> => {
    setEvents(prevEvents => prevEvents.map(event => {
      if (event.id === eventId) {
        const rsvped = event.rsvps.includes(userId);
        return {
          ...event,
          rsvps: rsvped ? event.rsvps.filter(id => id !== userId) : [...event.rsvps, userId]
        };
      }
      return event;
    }));
    // Update user's RSVP list
    updateUserProfile(userId, { 
      rsvpedEventIds: currentUser?.rsvpedEventIds?.includes(eventId) 
        ? currentUser.rsvpedEventIds.filter(id => id !== eventId) 
        : [...(currentUser?.rsvpedEventIds || []), eventId]
    });
    return true;
  }, [updateUserProfile, currentUser]);
  
  const followArtist = useCallback(async (fanId: string, artistId: string): Promise<boolean> => {
    updateUserProfile(fanId, {
      followedArtistIds: currentUser?.followedArtistIds?.includes(artistId)
        ? currentUser.followedArtistIds.filter(id => id !== artistId)
        : [...(currentUser?.followedArtistIds || []), artistId]
    });
    return true;
  }, [updateUserProfile, currentUser]);

  const savePost = useCallback(async (fanId: string, postId: string): Promise<boolean> => {
     updateUserProfile(fanId, {
      savedPostIds: currentUser?.savedPostIds?.includes(postId)
        ? currentUser.savedPostIds.filter(id => id !== postId)
        : [...(currentUser?.savedPostIds || []), postId]
    });
    // Also update post's like count (simplified)
    setPosts(prevPosts => prevPosts.map(p => {
      if (p.id === postId) {
        return { ...p, likes: currentUser?.savedPostIds?.includes(postId) ? p.likes - 1 : p.likes + 1 };
      }
      return p;
    }));
    return true;
  }, [updateUserProfile, currentUser]);


  return (
    <AppContext.Provider value={{ 
        currentUser, users, posts, events, 
        login, signup, logout, updateUserProfile, getUserById,
        createPost, getPosts, createEvent, getEvents,
        rsvpToEvent, followArtist, savePost
    }}>
      {isInitialized ? children : <div className="flex items-center justify-center h-screen"><SpinnerIcon className="w-12 h-12 text-primary" /></div>}
    </AppContext.Provider>
  );
};

export const useApp = (): AppContextType => {
  const context = React.useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

const SpinnerIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={`animate-spin h-5 w-5 ${className}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);
