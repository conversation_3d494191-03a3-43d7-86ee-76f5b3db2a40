
import React from 'react';
import { Link } from 'react-router-dom';
import { Button, FeedItem, EventItem, Card, Icon } from '../components';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { PostCategory } from '../types';

const HomePage: React.FC = () => {
  const { getPosts, getEvents, getUserById } = useApp();
  const latestPosts = getPosts().slice(0, 3);
  const upcomingEvents = getEvents().slice(0, 2);

  return (
    <>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary via-orange-500 to-secondary text-white py-24 px-4 text-center"> {/* Updated gradient */}
        <div className="max-w-4xl mx-auto">
          {/* Optional: Add logo here if desired on hero, smaller version */}
          {/* <img src="/public/logo.png" alt="MusicArt Club" className="h-20 mx-auto mb-6"/> */}
          <h1 className="text-5xl md:text-6xl font-bold mb-4">
            You Love <span className="text-accent">MUSIC</span> – You Love <span className="text-accent">ART</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8">
            Join The MusicArt Club: A New Creative Community for Artists, Educators & Fans.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent">
              <Link to="/auth?action=signup&role=Artist">Join as an Artist</Link>
            </Button>
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent">
              <Link to="/auth?action=signup&role=Educator">Join as an Educator</Link>
            </Button>
          </div>
          <div className="mt-8 flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-dark">
              <Link to="/explore">Browse the Feed</Link>
            </Button>
             <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-dark">
              <Link to="/events">Discover Workshops</Link>
            </Button>
          </div>
        </div>
      </div>

      <PageContainer>
        {/* Featured Posts Section */}
        <section className="mt-12">
          <h2 className="text-3xl font-semibold text-neutral-darkest mb-6 text-center">Fresh from the Studio</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {latestPosts.length > 0 ? latestPosts.map(post => (
              <FeedItem key={post.id} post={post} author={getUserById(post.userId) || undefined} />
            )) : <p className="text-neutral-dark col-span-full text-center">No posts yet. Be the first to share!</p>}
          </div>
          {latestPosts.length > 0 && (
            <div className="text-center mt-8">
              <Button variant="primary" size="lg">
                <Link to="/explore">Explore More Art & Music</Link>
              </Button>
            </div>
          )}
        </section>

        {/* Upcoming Events Section */}
        <section className="mt-16">
          <h2 className="text-3xl font-semibold text-neutral-darkest mb-6 text-center">Upcoming Events & Workshops</h2>
          <div className="grid md:grid-cols-2 gap-6">
             {upcomingEvents.length > 0 ? upcomingEvents.map(event => (
              <EventItem key={event.id} event={event} educator={getUserById(event.educatorId) || undefined} />
            )) : <p className="text-neutral-dark col-span-full text-center">No upcoming events. Check back soon!</p>}
          </div>
           {upcomingEvents.length > 0 && (
            <div className="text-center mt-8">
              <Button variant="primary" size="lg">
                <Link to="/events">See All Events</Link>
              </Button>
            </div>
          )}
        </section>

        {/* Why Join Section */}
        <section className="mt-16 py-12 bg-neutral-light rounded-lg">
          <h2 className="text-3xl font-semibold text-neutral-darkest mb-8 text-center">Why Join MusicArt Club?</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto px-4">
            <Card className="p-6 text-center hover:shadow-2xl transition-shadow duration-300">
              <Icon name="users" className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Connect & Collaborate</h3>
              <p className="text-neutral-dark">Meet fellow artists, educators, and fans. Find your next collaborator or student.</p>
            </Card>
            <Card className="p-6 text-center hover:shadow-2xl transition-shadow duration-300">
              <Icon name="photo" className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Showcase Your Talent</h3>
              <p className="text-neutral-dark">Share your music, visual art, and performances with a supportive community.</p>
            </Card>
            <Card className="p-6 text-center hover:shadow-2xl transition-shadow duration-300">
              <Icon name="academicCap" className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Learn & Grow</h3>
              <p className="text-neutral-dark">Discover workshops, lessons, and resources to expand your creative skills.</p>
            </Card>
          </div>
        </section>
      </PageContainer>
    </>
  );
};

export default HomePage;
